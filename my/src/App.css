/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.App {
  min-height: 100vh;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.landing-page .container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.landing-page h1 {
  color: #4a5568;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  color: #718096;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.quiz-form {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4a5568;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #e53e3e;
}

.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 5px;
  display: block;
}

.submit-error {
  text-align: center;
  margin: 15px 0;
  padding: 10px;
  background: #fed7d7;
  border-radius: 6px;
}

.generate-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-top: 20px;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Quiz Page Styles */
.quiz-page {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.quiz-page .container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-top: 20px;
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.quiz-info h2 {
  color: #4a5568;
  font-size: 1.8rem;
  margin-bottom: 5px;
}

.quiz-meta {
  display: flex;
  gap: 20px;
  color: #718096;
  font-size: 14px;
}

.timer {
  background: #f7fafc;
  padding: 10px 15px;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  font-weight: 600;
  color: #4a5568;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin-bottom: 30px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.question-container {
  margin-bottom: 30px;
}

.question-text {
  font-size: 1.3rem;
  color: #2d3748;
  margin-bottom: 25px;
  line-height: 1.5;
}

.answers-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-option {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.answer-option:hover {
  border-color: #667eea;
  background: #f7fafc;
}

.answer-option.selected {
  border-color: #667eea;
  background: #edf2f7;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.option-letter {
  background: #667eea;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 15px;
  flex-shrink: 0;
}

.answer-option.selected .option-letter {
  background: #4c51bf;
}

.option-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.4;
}

.quiz-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.nav-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.prev-btn,
.next-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.prev-btn:hover:not(:disabled),
.next-btn:hover:not(:disabled) {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.submit-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(72, 187, 120, 0.3);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.question-indicators {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.question-indicator {
  width: 35px;
  height: 35px;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  background: white;
  color: #718096;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-indicator.current {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.question-indicator.answered {
  border-color: #48bb78;
  background: #48bb78;
  color: white;
}

.question-indicator:hover {
  transform: scale(1.1);
}

/* Results Page Styles */
.results-page {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.results-page .container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  margin-top: 20px;
}

.results-header {
  text-align: center;
  margin-bottom: 40px;
}

.results-header h1 {
  color: #4a5568;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.results-header h2 {
  color: #667eea;
  font-size: 1.8rem;
  margin-bottom: 5px;
}

.results-header p {
  color: #718096;
  font-size: 1.1rem;
}

.score-section {
  text-align: center;
  margin-bottom: 40px;
}

.score-circle {
  width: 200px;
  height: 200px;
  border: 8px solid;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  background: #f7fafc;
}

.score-number {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.score-text {
  color: #718096;
  font-size: 1rem;
  font-weight: 600;
}

.score-message h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.detailed-results {
  margin-bottom: 40px;
}

.detailed-results h3 {
  color: #4a5568;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-align: center;
}

.questions-review {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-review {
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  padding: 20px;
  background: #f7fafc;
}

.question-review.correct {
  border-color: #48bb78;
  background: #f0fff4;
}

.question-review.incorrect {
  border-color: #f56565;
  background: #fffaf0;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-number {
  font-weight: 600;
  color: #4a5568;
}

.result-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.result-indicator.correct {
  background: #48bb78;
  color: white;
}

.result-indicator.incorrect {
  background: #f56565;
  color: white;
}

.question-text {
  font-size: 1.1rem;
  color: #2d3748;
  margin-bottom: 15px;
  font-weight: 500;
}

.answer-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-answer,
.correct-answer,
.explanation {
  font-size: 14px;
}

.correct-answer-text {
  color: #48bb78;
  font-weight: 600;
}

.wrong-answer {
  color: #f56565;
  font-weight: 600;
}

.explanation {
  background: #edf2f7;
  padding: 10px;
  border-radius: 6px;
  border-left: 4px solid #667eea;
  margin-top: 10px;
}

.performance-stats {
  margin-bottom: 40px;
}

.performance-stats h3 {
  color: #4a5568;
  font-size: 1.5rem;
  margin-bottom: 20px;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  background: #f7fafc;
  padding: 20px;
  border-radius: 10px;
  border: 2px solid #e2e8f0;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  color: #718096;
  font-size: 14px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.retake-btn,
.new-quiz-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retake-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.retake-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.new-quiz-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.new-quiz-btn:hover {
  background: #cbd5e0;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .landing-page .container {
    padding: 30px 20px;
  }

  .landing-page h1 {
    font-size: 2rem;
  }

  .quiz-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .quiz-info h2 {
    font-size: 1.5rem;
  }

  .quiz-meta {
    flex-direction: column;
    gap: 5px;
  }

  .timer {
    align-self: stretch;
    text-align: center;
  }

  .question-text {
    font-size: 1.1rem;
  }

  .answer-option {
    padding: 12px 15px;
  }

  .option-text {
    font-size: 14px;
  }

  .quiz-navigation {
    flex-direction: column;
    gap: 15px;
  }

  .question-indicators {
    justify-content: center;
  }

  .nav-btn {
    width: 100%;
    max-width: 200px;
  }

  .score-circle {
    width: 150px;
    height: 150px;
  }

  .score-number {
    font-size: 2.5rem;
  }

  .results-header h1 {
    font-size: 2rem;
  }

  .results-header h2 {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .retake-btn,
  .new-quiz-btn {
    width: 100%;
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .landing-page h1 {
    font-size: 1.8rem;
  }

  .quiz-info h2 {
    font-size: 1.3rem;
  }

  .question-text {
    font-size: 1rem;
  }

  .answer-option {
    padding: 10px 12px;
  }

  .option-letter {
    width: 25px;
    height: 25px;
    font-size: 12px;
    margin-right: 10px;
  }

  .option-text {
    font-size: 13px;
  }

  .question-indicator {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }

  .score-circle {
    width: 120px;
    height: 120px;
  }

  .score-number {
    font-size: 2rem;
  }

  .score-text {
    font-size: 0.9rem;
  }

  .results-header h1 {
    font-size: 1.8rem;
  }

  .results-header h2 {
    font-size: 1.3rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .question-review {
    padding: 15px;
  }
}