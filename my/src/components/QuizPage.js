import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const QuizPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [quizStarted, setQuizStarted] = useState(false);

  // Get quiz data from navigation state
  const quizData = location.state?.quizData;
  const formData = location.state?.formData;

  // Helper function to get questions array from any data structure
  const getQuestions = (data) => {
    if (!data) return null;

    if (Array.isArray(data)) {
      return data;
    } else if (data.questions && Array.isArray(data.questions)) {
      return data.questions;
    } else {
      const possibleKeys = ['quiz', 'data', 'items', 'results'];
      for (const key of possibleKeys) {
        if (data[key] && Array.isArray(data[key])) {
          return data[key];
        }
      }
    }
    return null;
  };

  const questions = getQuestions(quizData);

  useEffect(() => {
    console.log('QuizPage received data:', quizData);

    // Redirect to landing page if no quiz data
    if (!quizData) {
      console.log('No quiz data, redirecting to landing page');
      navigate('/');
      return;
    }

    if (!questions || questions.length === 0) {
      console.log('No valid questions found, redirecting to landing page');
      navigate('/');
      return;
    }

    console.log('Found questions:', questions);

    // Set timer for 30 seconds per question
    const totalTime = questions.length * 30;
    setTimeRemaining(totalTime);
    setQuizStarted(true);
  }, [quizData, questions, navigate]);

  const handleSubmitQuiz = useCallback(() => {
    // Calculate score
    let correctAnswers = 0;
    questions.forEach((question, index) => {
      // Handle different possible property names for correct answer
      const correctAnswer = question.correct_answer || question.answer || question.correctAnswer;
      if (answers[index] === correctAnswer) {
        correctAnswers++;
      }
    });

    const score = Math.round((correctAnswers / questions.length) * 100);

    // Navigate to results page
    navigate('/results', {
      state: {
        score,
        correctAnswers,
        totalQuestions: questions.length,
        answers,
        questions: questions,
        formData
      }
    });
  }, [answers, questions, navigate, formData]);

  useEffect(() => {
    if (!quizStarted || timeRemaining === null || timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          handleSubmitQuiz();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [quizStarted, timeRemaining, handleSubmitQuiz]);

  const handleAnswerSelect = (questionIndex, selectedAnswer) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: selectedAnswer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };



  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!questions || questions.length === 0) {
    return (
      <div className="quiz-page">
        <div className="container">
          <h2>No quiz data found</h2>
          <button onClick={() => navigate('/')}>Go Back to Home</button>
        </div>
      </div>
    );
  }

  const currentQuestionData = questions[currentQuestion];
  const progress = ((currentQuestion + 1) / questions.length) * 100;

  return (
    <div className="quiz-page">
      <div className="container">
        <div className="quiz-header">
          <div className="quiz-info">
            <h2>{formData?.topic} Quiz</h2>
            <div className="quiz-meta">
              <span>Question {currentQuestion + 1} of {questions.length}</span>
              <span>Difficulty: {formData?.difficulty}</span>
            </div>
          </div>
          
          <div className="timer">
            <span>Time Remaining: {formatTime(timeRemaining || 0)}</span>
          </div>
        </div>

        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <div className="question-container">
          <h3 className="question-text">{currentQuestionData.question}</h3>
          
          <div className="answers-container">
            {currentQuestionData.options.map((option, index) => (
              <button
                key={index}
                className={`answer-option ${
                  answers[currentQuestion] === option ? 'selected' : ''
                }`}
                onClick={() => handleAnswerSelect(currentQuestion, option)}
              >
                <span className="option-letter">{String.fromCharCode(65 + index)}</span>
                <span className="option-text">{option}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="quiz-navigation">
          <button 
            onClick={handlePreviousQuestion}
            disabled={currentQuestion === 0}
            className="nav-btn prev-btn"
          >
            Previous
          </button>
          
          <div className="question-indicators">
            {questions.map((_, index) => (
              <button
                key={index}
                className={`question-indicator ${
                  index === currentQuestion ? 'current' : ''
                } ${
                  answers[index] !== undefined ? 'answered' : ''
                }`}
                onClick={() => setCurrentQuestion(index)}
              >
                {index + 1}
              </button>
            ))}
          </div>

          {currentQuestion === questions.length - 1 ? (
            <button 
              onClick={handleSubmitQuiz}
              className="nav-btn submit-btn"
            >
              Submit Quiz
            </button>
          ) : (
            <button 
              onClick={handleNextQuestion}
              className="nav-btn next-btn"
            >
              Next
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuizPage;
