import { useLocation, useNavigate } from 'react-router-dom';

const ResultsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const {
    score,
    correctAnswers,
    totalQuestions,
    answers,
    questions,
    formData
  } = location.state || {};

  // Redirect if no results data
  if (!location.state) {
    navigate('/');
    return null;
  }

  const getScoreMessage = (score) => {
    if (score >= 90) return "Excellent! Outstanding performance!";
    if (score >= 80) return "Great job! Very good performance!";
    if (score >= 70) return "Good work! Solid performance!";
    if (score >= 60) return "Not bad! Room for improvement.";
    return "Keep studying! You'll do better next time.";
  };

  const getScoreColor = (score) => {
    if (score >= 90) return "#4CAF50"; // Green
    if (score >= 80) return "#8BC34A"; // Light Green
    if (score >= 70) return "#FFC107"; // Yellow
    if (score >= 60) return "#FF9800"; // Orange
    return "#F44336"; // Red
  };

  const handleRetakeQuiz = () => {
    navigate('/', { 
      state: { 
        prefillData: formData 
      } 
    });
  };

  const handleNewQuiz = () => {
    navigate('/');
  };

  return (
    <div className="results-page">
      <div className="container">
        <div className="results-header">
          <h1>Quiz Results</h1>
          <div className="quiz-info">
            <h2>{formData?.topic} Quiz</h2>
            <p>Difficulty: {formData?.difficulty} | Language: {formData?.language}</p>
          </div>
        </div>

        <div className="score-section">
          <div className="score-circle" style={{ borderColor: getScoreColor(score) }}>
            <div className="score-number" style={{ color: getScoreColor(score) }}>
              {score}%
            </div>
            <div className="score-text">
              {correctAnswers} out of {totalQuestions} correct
            </div>
          </div>
          
          <div className="score-message">
            <h3 style={{ color: getScoreColor(score) }}>
              {getScoreMessage(score)}
            </h3>
          </div>
        </div>

        <div className="detailed-results">
          <h3>Question Review</h3>
          <div className="questions-review">
            {questions?.map((question, index) => {
              const userAnswer = answers[index];

              // Handle different possible property names for correct answer
              const correctAnswer = question.correct_answer || question.answer || question.correctAnswer;
              const isCorrect = userAnswer === correctAnswer;

              // Debug logging
              console.log(`Question ${index + 1}:`, {
                question: question.question,
                userAnswer,
                correctAnswer,
                isCorrect,
                questionObject: question
              });

              return (
                <div key={index} className={`question-review ${isCorrect ? 'correct' : 'incorrect'}`}>
                  <div className="question-header">
                    <span className="question-number">Question {index + 1}</span>
                    <span className={`result-indicator ${isCorrect ? 'correct' : 'incorrect'}`}>
                      {isCorrect ? '✓' : '✗'}
                    </span>
                  </div>

                  <div className="question-text">
                    {question.question}
                  </div>

                  <div className="answer-details">
                    <div className="user-answer">
                      <strong>Your answer:</strong>
                      <span className={isCorrect ? 'correct-answer' : 'wrong-answer'}>
                        {userAnswer || 'No answer selected'}
                      </span>
                    </div>

                    {!isCorrect && (
                      <div className="correct-answer">
                        <strong>Correct answer:</strong>
                        <span className="correct-answer">{correctAnswer}</span>
                      </div>
                    )}

                    {question.explanation && (
                      <div className="explanation">
                        <strong>Explanation:</strong> {question.explanation}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="performance-stats">
          <h3>Performance Summary</h3>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">{correctAnswers}</div>
              <div className="stat-label">Correct Answers</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{totalQuestions - correctAnswers}</div>
              <div className="stat-label">Incorrect Answers</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{score}%</div>
              <div className="stat-label">Accuracy</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">{totalQuestions}</div>
              <div className="stat-label">Total Questions</div>
            </div>
          </div>
        </div>

        <div className="action-buttons">
          <button 
            onClick={handleRetakeQuiz}
            className="retake-btn"
          >
            Create Quiz
          </button>
          <button 
            onClick={handleNewQuiz}
            className="new-quiz-btn"
          >
            Create New Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResultsPage;
