import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const LandingPage = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    topic: '',
    numQuestions: 5,
    difficulty: 'Medium',
    language: 'English'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const difficulties = ['Easy', 'Medium', 'Hard'];
  const languages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese'];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.topic.trim()) {
      newErrors.topic = 'Topic is required';
    }
    
    if (formData.numQuestions < 1 || formData.numQuestions > 20) {
      newErrors.numQuestions = 'Number of questions must be between 1 and 20';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      const response = await axios.post('http://localhost:3002/generate_quiz', {
        query: `Please provide an array of ${formData.numQuestions} objects, each representing a ${formData.difficulty} difficulty quiz on the topic ${formData.topic} in ${formData.language}. Each object should include the keys question, options, and answer in JSON format. The options key should contain an array of strings representing the answer choices, and the answer key should specify the correct answer. Exclude any additional keys or structures.`,
        language: formData.language
      });

      console.log('API Response:', response.data);

      // Check if response has the expected structure
      let quizData = response.data;

      // If the response is an array directly, wrap it in an object
      if (Array.isArray(response.data)) {
        quizData = { questions: response.data };
      }
      // If the response has a different structure, try to find the questions array
      else if (response.data && !response.data.questions) {
        // Look for common property names that might contain the questions
        const possibleKeys = ['quiz', 'questions', 'data', 'items'];
        for (const key of possibleKeys) {
          if (response.data[key] && Array.isArray(response.data[key])) {
            quizData = { questions: response.data[key] };
            break;
          }
        }
      }

      console.log('Processed Quiz Data:', quizData);

      // Navigate to quiz page with the quiz data
      navigate('/quiz', {
        state: {
          quizData: quizData,
          formData: formData
        }
      });
    } catch (error) {
      console.error('Error generating quiz:', error);
      setErrors({ 
        submit: 'Failed to generate quiz. Please try again.' 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="landing-page">
      <div className="container">
        <h1>Quiz Generator</h1>
        <p className="subtitle">Create a personalized quiz on any topic</p>
        
        <form onSubmit={handleSubmit} className="quiz-form">
          <div className="form-group">
            <label htmlFor="topic">Topic *</label>
            <input
              type="text"
              id="topic"
              name="topic"
              value={formData.topic}
              onChange={handleInputChange}
              placeholder="Enter quiz topic (e.g., JavaScript, History, Science)"
              className={errors.topic ? 'error' : ''}
            />
            {errors.topic && <span className="error-message">{errors.topic}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="numQuestions">Number of Questions *</label>
            <input
              type="number"
              id="numQuestions"
              name="numQuestions"
              value={formData.numQuestions}
              onChange={handleInputChange}
              min="1"
              max="20"
              className={errors.numQuestions ? 'error' : ''}
            />
            {errors.numQuestions && <span className="error-message">{errors.numQuestions}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="difficulty">Difficulty Level</label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleInputChange}
            >
              {difficulties.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="language">Language</label>
            <select
              id="language"
              name="language"
              value={formData.language}
              onChange={handleInputChange}
            >
              {languages.map(lang => (
                <option key={lang} value={lang}>{lang}</option>
              ))}
            </select>
          </div>

          {errors.submit && <div className="error-message submit-error">{errors.submit}</div>}

          <button 
            type="submit" 
            className="generate-btn"
            disabled={loading}
          >
            {loading ? 'Generating Quiz...' : 'Generate Quiz'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LandingPage;
