import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const LandingPage = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    topic: '',
    numQuestions: 5,
    difficulty: 'Medium',
    language: 'English'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const difficulties = ['Easy', 'Medium', 'Hard'];
  const languages = ['English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese'];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.topic.trim()) {
      newErrors.topic = 'Topic is required';
    }
    
    if (formData.numQuestions < 1 || formData.numQuestions > 20) {
      newErrors.numQuestions = 'Number of questions must be between 1 and 20';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      const response = await axios.post(process.env.REACT_APP_API_URL, {
        query: `Please provide an array of ${formData.numQuestions} objects, each representing a ${formData.difficulty}  quiz on the topic ${formData.topic} in ${formData.language}. Each object should include the keys question, options, and answer in JSON format. The options key should contain an array of strings representing the answer choices, and the answer key should specify the correct answer. Exclude any additional keys or structures.`
        
      });

      console.log('API Response:', response.data);

      // Ensure we have valid quiz data before navigating
      if (response.data.quizzes) {
        // Navigate to quiz page with the quiz data
        console.log('Navigating to quiz page...');
        navigate('/quiz', {
          state: {
            quizData: response.data.quizzes,
            formData: formData
          }
        });
      } else {
        throw new Error('No quiz data received from API');
      }
    } catch (error) {
      console.error('Error generating quiz:', error);

      let errorMessage = 'Failed to generate quiz. Please try again.';

      if (error.response) {
        const { status, data } = error.response;

        if (status === 503) {
          errorMessage = 'AI service is temporarily overloaded. Please try again in a few moments.';
        } else if (status === 429) {
          errorMessage = 'Too many requests. Please wait a minute before trying again.';
        } else if (data && data.details) {
          errorMessage = data.details;
        }
      }

      setErrors({
        submit: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="landing-page">
      <div className="container">
        <h1>Quiz Generator</h1>
        <p className="subtitle">Create a personalized quiz on any topic</p>
        
        <form onSubmit={handleSubmit} className="quiz-form">
          <div className="form-group">
            <label htmlFor="topic">Topic *</label>
            <input
              type="text"
              id="topic"
              name="topic"
              value={formData.topic}
              onChange={handleInputChange}
              placeholder="Enter quiz topic (e.g., JavaScript, History, Science)"
              className={errors.topic ? 'error' : ''}
            />
            {errors.topic && <span className="error-message">{errors.topic}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="numQuestions">Number of Questions *</label>
            <input
              type="number"
              id="numQuestions"
              name="numQuestions"
              value={formData.numQuestions}
              onChange={handleInputChange}
              min="1"
              max="20"
              className={errors.numQuestions ? 'error' : ''}
            />
            {errors.numQuestions && <span className="error-message">{errors.numQuestions}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="difficulty">Difficulty Level</label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleInputChange}
            >
              {difficulties.map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="language">Language</label>
            <select
              id="language"
              name="language"
              value={formData.language}
              onChange={handleInputChange}
            >
              {languages.map(lang => (
                <option key={lang} value={lang}>{lang}</option>
              ))}
            </select>
          </div>

          {errors.submit && <div className="error-message submit-error">{errors.submit}</div>}

          <button 
            type="submit" 
            className="generate-btn"
            disabled={loading}
          >
            {loading ? 'Generating Quiz...' : 'Generate Quiz'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LandingPage;
