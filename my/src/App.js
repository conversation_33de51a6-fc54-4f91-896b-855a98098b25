import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';

// Lazy load components for better performance
const LandingPage = React.lazy(() => import('./components/LandingPage'));
const QuizPage = React.lazy(() => import('./components/QuizPage'));
const ResultsPage = React.lazy(() => import('./components/ResultsPage'));

// Loading component for route transitions
const RouteLoader = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '50vh',
    fontSize: '16px',
    color: '#666'
  }}>
    <div>
      <div style={{
        border: '3px solid #f3f3f3',
        borderTop: '3px solid #3498db',
        borderRadius: '50%',
        width: '30px',
        height: '30px',
        animation: 'spin 1s linear infinite',
        margin: '0 auto 15px'
      }}></div>
      Loading...
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <div className="App">
        <Suspense fallback={<RouteLoader />}>
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/quiz" element={<QuizPage />} />
            <Route path="/results" element={<ResultsPage />} />
          </Routes>
        </Suspense>
      </div>
    </Router>
  );
}

export default App;