import logo from './logo.svg'
import { useEffect, useState } from 'react';
import axios from 'axios';
import './App.css';

function App() {
  const [data, setData] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await axios.get("https://fakestoreapi.com/products?limit=15");
        console.log(res.data);
        setData(res.data);
      } catch (err) {
        console.error(err);
      }
    };

    fetchData();
  }, []);

  const showMore = () => {
    setIsVisible(true);
  };

  return (
    <div className="flex">
      {!isVisible && (
        <p style={{ cursor: "pointer", color: "blue" }} onClick={showMore}>
          View More
        </p>
      )}

      {data.map((item, index) => {
        if (!isVisible && index >= 5) return null; // Show only first 5 until "view more"
        return (
          <div key={item.id} style={{ border: '1px solid #ccc', margin: '10px', padding: '10px' }}>
            <h2>{item.title}</h2>
            <p>{item.description}</p>
            <button>Open</button>
          </div>
        );
      })}
    </div>
  );
}

export default App;