# AI Quiz Generator

A React-based quiz application that generates personalized quizzes on any topic using AI.

## Features

- 🎯 **Custom Quiz Generation** - Create quizzes on any topic
- 🌍 **Multi-language Support** - Generate quizzes in different languages
- 📊 **Difficulty Levels** - Choose from Easy, Medium, or Hard
- ⏱️ **Timed Quizzes** - 30 seconds per question
- 📈 **Detailed Results** - See your score and review answers
- 📱 **Responsive Design** - Works on desktop and mobile

## Setup Instructions

### 1. Clone the Repository
```bash
git clone https://github.com/shadowfax29/ai_quiz.git
cd ai_quiz
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your API endpoint
REACT_APP_API_URL=your_api_endpoint_here
```

### 4. Start the Development Server
```bash
npm start
```

The app will open at `http://localhost:3000`

## Usage

1. **Create a Quiz**
   - Enter a topic (e.g., "JavaScript", "History", "Science")
   - Select difficulty level
   - Choose number of questions
   - Select language

2. **Take the Quiz**
   - Answer questions within the time limit
   - Navigate between questions
   - Submit when complete

3. **View Results**
   - See your score and performance
   - Review correct/incorrect answers
   - Retake or create new quiz

## Technologies Used

- **React 19** - Frontend framework
- **React Router** - Navigation
- **Axios** - HTTP requests
- **CSS3** - Styling and animations

## Project Structure

```
src/
├── components/
│   ├── LandingPage.js    # Quiz configuration form
│   ├── QuizPage.js       # Quiz taking interface
│   └── ResultsPage.js    # Results and review
├── App.js                # Main app component
├── App.css              # Global styles
└── index.js             # App entry point
```

## API Integration

The app integrates with an AI-powered quiz generation API that:
- Accepts topic, difficulty, and language parameters
- Returns structured quiz questions with multiple choice answers
- Supports various question formats and topics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this project for learning and development.
