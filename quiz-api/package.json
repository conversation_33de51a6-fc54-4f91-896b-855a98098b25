{"name": "quiz-api", "version": "1.0.0", "description": "Node.js API for quiz generation using OpenAI", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["quiz", "api", "openai", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.2.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}