// server.js
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();
const app = express();
const port = 3002;

// Enable CORS for all origins (no authorization required)
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Accept'],
}));
app.use(bodyParser.json());

// Initialize Google Gemini AI client
const genAI = new GoogleGenerativeAI('AIzaSyB4H1nycGCxFdVwVGm1vISGTngnWB5Oypw');
const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is healthy!' });
});

// Quiz generator endpoint
app.post('/generate_quiz', async (req, res) => {
  try {
    const { query, language } = req.body;

    // Create the prompt for Gemini
    const prompt = `You are a helpful assistant that responds in ${language}. ${query}

Please respond with valid JSON only, no additional text or formatting. The response should be a JSON object containing an array of quiz questions.`;

    // Generate content using Gemini
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('Gemini response:', text);

    // Parse the JSON response to validate it
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(text);
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', parseError);
      return res.status(500).json({
        error: 'Invalid JSON response from AI',
        details: parseError.message
      });
    }

    res.setHeader('Content-Type', 'application/json');
    res.json(parsedResponse);
  } catch (err) {
    console.error('Gemini error:', err);
    res.status(500).json({ error: 'Failed to generate quiz', details: err.message });
  }
});

app.listen(port, () => {
  console.log(`✅ Quiz API server running at http://localhost:${port}`);
});
