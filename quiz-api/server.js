// server.js
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();
const app = express();
const port = 3002;

// Enable CORS for all origins (no authorization required)
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Accept'],
}));
app.use(bodyParser.json());

// Initialize Google Gemini AI client
const genAI = new GoogleGenerativeAI('AIzaSyB4H1nycGCxFdVwVGm1vISGTngnWB5Oypw');
const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is healthy!' });
});

// Helper function to retry API calls
async function retryApiCall(apiCall, maxRetries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      console.log(`Attempt ${attempt} failed:`, error.message);

      if (attempt === maxRetries) {
        throw error;
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
}

// Quiz generator endpoint
app.post('/generate_quiz', async (req, res) => {
  try {
    const { query, language } = req.body;

    // Create the prompt for Gemini
    const prompt = `You are a helpful assistant that responds in ${language}. ${query}

Please respond with valid JSON only, no additional text or formatting. The response should be a JSON object containing an array of quiz questions.`;

    console.log('Attempting to generate quiz with Gemini...');

    // Generate content using Gemini with retry logic
    const result = await retryApiCall(async () => {
      return await model.generateContent(prompt);
    }, 3, 2000); // 3 retries with 2 second delays

    const response = await result.response;
    let text = response.text();

    console.log('Gemini raw response:', text);

    // Clean the response - remove markdown code blocks if present
    text = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();

    console.log('Cleaned response:', text);

    // Parse the JSON response to validate it
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(text);
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', parseError);
      console.error('Raw text:', text);
      return res.status(500).json({
        error: 'Invalid JSON response from AI',
        details: parseError.message,
        rawResponse: text
      });
    }

    res.setHeader('Content-Type', 'application/json');
    res.json(parsedResponse);
  } catch (err) {
    console.error('Gemini error:', err);

    // Handle specific error types
    if (err.message.includes('503') || err.message.includes('overloaded')) {
      return res.status(503).json({
        error: 'AI service temporarily unavailable',
        details: 'The AI service is currently overloaded. Please try again in a few moments.',
        retryAfter: 30
      });
    }

    if (err.message.includes('429')) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        details: 'Too many requests. Please wait before trying again.',
        retryAfter: 60
      });
    }

    res.status(500).json({
      error: 'Failed to generate quiz',
      details: err.message
    });
  }
});

app.listen(port, () => {
  console.log(`✅ Quiz API server running at http://localhost:${port}`);
});
