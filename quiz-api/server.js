// server.js
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { OpenAI } = require('openai');
require('dotenv').config();
const app = express();
const port = 3002;

app.use(cors());
app.use(bodyParser.json());

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Load from .env or environment
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is healthy!' });
});

// Quiz generator endpoint
app.post('/generate_quiz', async (req, res) => {
  try {
    const { query, language } = req.body;

    const chatResponse = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: `You are a helpful assistant that responds in ${language}` },
        { role: 'user', content: query }
      ],
      response_format: 'json',
    });

    const reply = chatResponse.choices[0].message.content;
    res.setHeader('Content-Type', 'application/json');
    res.send(reply);
  } catch (err) {
    console.error('OpenAI error:', err);
    res.status(500).json({ error: 'Failed to generate quiz', details: err.message });
  }
});

app.listen(port, () => {
  console.log(`✅ Quiz API server running at http://localhost:${port}`);
});
