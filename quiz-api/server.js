const express = require('express');
const cors = require('cors');
const OpenAI = require('openai');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Charset'],
}));

app.use(express.json({ charset: 'utf-8' }));

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Quiz generation endpoint
app.post('/generate_quiz', async (req, res) => {
  try {
    const { query, language = 'English' } = req.body;

    if (!query) {
      return res.status(400).json({ 
        error: 'Query parameter is required' 
      });
    }

    console.log('Received request:', { query, language });

    const chatCompletion = await openai.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `You are a helpful assistant that responds in ${language}. You generate quiz questions in JSON format.`
        },
        {
          role: "user",
          content: query
        }
      ],
      model: 'gpt-4o',
      response_format: {
        type: "json_object"
      }
    });

    const reply = chatCompletion.choices[0].message.content;
    console.log("OpenAI reply:", reply);

    // Parse the JSON response to validate it
    let parsedReply;
    try {
      parsedReply = JSON.parse(reply);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', parseError);
      return res.status(500).json({ 
        error: 'Invalid JSON response from AI' 
      });
    }

    // Set response headers
    res.set({
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*'
    });

    // Return the parsed JSON
    res.json(parsedReply);

  } catch (error) {
    console.error('Error generating quiz:', error);
    res.status(500).json({ 
      error: 'Failed to generate quiz',
      details: error.message 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Quiz API is running' });
});

// Handle OPTIONS requests
app.options('*', (req, res) => {
  res.set({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept-Charset',
    'Accept-Charset': 'utf-8'
  });
  res.status(200).end();
});

// Start server
app.listen(PORT, () => {
  console.log(`Quiz API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Quiz endpoint: http://localhost:${PORT}/generate_quiz`);
});

module.exports = app;
