# Quiz API - Node.js

A Node.js API that replicates the Deno/Supabase quiz generation function using OpenAI.

## Features

- Generate quiz questions using OpenAI GPT-4
- CORS enabled for cross-origin requests
- JSON response format
- Multi-language support
- No authorization required
- Health check endpoint

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment Configuration:**
   - Copy `.env.example` to `.env`
   - Add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   PORT=3002
   ```

3. **Start the server:**
   ```bash
   # Production
   npm start
   
   # Development (with auto-reload)
   npm run dev
   ```

## API Endpoints

### POST /generate_quiz

Generate quiz questions based on a query.

**Request Body:**
```json
{
  "query": "Please provide an array of 5 objects, each representing a Medium difficulty quiz on the topic JavaScript in English...",
  "language": "English"
}
```

**Response:**
```json
{
  "questions": [
    {
      "question": "What is a closure in JavaScript?",
      "options": ["A", "B", "C", "D"],
      "answer": "A"
    }
  ]
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "OK",
  "message": "Quiz API is running"
}
```

## Usage Example

```bash
curl -X POST http://localhost:3002/generate_quiz \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Generate 3 easy JavaScript questions",
    "language": "English"
  }'
```

## Integration with React App

Update your React app's API endpoint from:
```javascript
'https://tkhqppfqsitovjvsstfl.supabase.co/functions/v1/generate_quiz'
```

To:
```javascript
'http://localhost:3002/generate_quiz'
```

## Dependencies

- **express**: Web framework
- **cors**: Cross-origin resource sharing
- **openai**: OpenAI API client
- **dotenv**: Environment variable management

## Development

- Uses nodemon for auto-reload during development
- Includes error handling and logging
- JSON response validation
