// testAPI.js
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002';

async function testAPI() {
  console.log('Testing Quiz API...\n');

  try {
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    console.log('2. Testing quiz generation...');
    const quizRequest = {
      query: "Please provide an array of 2 objects, each representing a Easy difficulty quiz on the topic JavaScript in English. Each object should include the keys question, options, and answer in JSON format. The options key should contain an array of strings representing the answer choices, and the answer key should specify the correct answer. Exclude any additional keys or structures.",
      language: "English"
    };

    const quizResponse = await axios.post(`${API_BASE_URL}/generate_quiz`, quizRequest);
    console.log('✅ Quiz generation successful!');
    console.log('Response:', JSON.stringify(quizResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

testAPI();
